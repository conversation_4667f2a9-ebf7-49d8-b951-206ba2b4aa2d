{"_comment": "Fallback OpenAPI specification used when the live server is not available during client generation (e.g., Docker builds). The server URL is dynamically updated based on REACT_APP_API_BASE_URL.", "openapi": "3.0.0", "info": {"title": "Crypto Indicator API", "description": "API for cryptocurrency technical indicators and statistics", "version": "1.0", "contact": {}}, "servers": [{"url": "http://localhost:6701"}], "tags": [{"name": "Crypto Statistics", "description": ""}], "paths": {"/api/v1/crypto/statistics": {"get": {"operationId": "CryptoStatisticsController_getCryptoStatistics", "summary": "Get cryptocurrency statistics", "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StructuredCryptoCurrencyStatisticsResultDto"}}}}}}, "tags": ["Crypto Statistics"]}}}, "components": {"schemas": {"LatestIndicatorDataDto": {"type": "object", "properties": {"close": {"type": "number"}, "color": {"type": "string", "enum": ["gold", "blue", "gray", "green", "red"]}, "high": {"type": "number"}, "hl2": {"type": "number"}, "low": {"type": "number"}, "marketCap": {"type": "number"}, "name": {"type": "string"}, "open": {"type": "number"}, "p1": {"type": "boolean"}, "p2": {"type": "boolean"}, "p3": {"type": "boolean"}, "smma15": {"type": "number"}, "smma19": {"type": "number"}, "smma25": {"type": "number"}, "smma29": {"type": "number"}, "timestamp": {"type": "string"}, "volume": {"type": "number"}}, "required": ["close", "color", "high", "hl2", "low", "marketCap", "name", "open", "p1", "p2", "p3", "smma15", "smma19", "smma25", "smma29", "timestamp", "volume"]}, "StructuredCryptoCurrencyStatisticsResultDto": {"type": "object", "properties": {"symbol": {"type": "string"}, "conversionCurrency": {"type": "number"}, "indicatorValues": {"$ref": "#/components/schemas/LatestIndicatorDataDto"}}, "required": ["symbol", "conversionCurrency", "indicatorValues"]}}}}