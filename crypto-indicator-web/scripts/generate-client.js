#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const http = require('http');

// Load environment variables from .env file
require('dotenv').config({ path: path.join(__dirname, '..', '..', '.env') });

const args = process.argv.slice(2);
const baseUrlArg = args.find(arg => arg.startsWith('--base-url='));

if (!process.env.REACT_APP_API_BASE_URL && !baseUrlArg) {
  console.error('Error: REACT_APP_API_BASE_URL environment variable is required');
  console.error('Please set it in your .env file or pass --base-url=<url>');
  process.exit(1);
}

const CLIENT_BASE_URL = baseUrlArg ? baseUrlArg.split('=')[1] : process.env.REACT_APP_API_BASE_URL;
const API_BASE_URL = process.env.API_BASE_URL;
if (!API_BASE_URL) {
  console.error('Error: API_BASE_URL environment variable is required');
  console.error('Please set it in your .env file');
  process.exit(1);
}

const SWAGGER_JSON_URL = `${API_BASE_URL}/api-docs-json`;
const OUTPUT_DIR = path.join(__dirname, '..', 'src', 'generated');

function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const req = http.get(url, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        if (res.statusCode === 200) {
          resolve(data);
        } else {
          reject(new Error(`HTTP ${res.statusCode}: ${res.statusMessage}`));
        }
      });
    });
    req.on('error', reject);
    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

function loadFallbackSpec() {
  const specPath = path.join(__dirname, 'openapi-spec.json');
  if (!fs.existsSync(specPath)) {
    throw new Error(`Fallback OpenAPI spec not found at: ${specPath}`);
  }

  const specContent = fs.readFileSync(specPath, 'utf8');
  const spec = JSON.parse(specContent);

  // Update the server URL to match the configured base URL
  spec.servers = [{ url: CLIENT_BASE_URL }];

  return spec;
}

async function loadOpenAPISpec() {
  try {
    console.log(`Fetching OpenAPI spec from: ${SWAGGER_JSON_URL}`);
    const specData = await makeRequest(SWAGGER_JSON_URL);
    const spec = JSON.parse(specData);
    if (!spec.openapi || !spec.paths || !spec.components) {
      throw new Error('Invalid OpenAPI specification');
    }
    console.log('Using live OpenAPI spec from server');
    return enhanceOpenAPISpec(spec);
  } catch (error) {
    console.log('Server not available, using fallback OpenAPI spec from file');
    return enhanceOpenAPISpec(loadFallbackSpec());
  }
}

function enhanceOpenAPISpec(spec) {
  if (!spec.servers || spec.servers.length === 0) {
    spec.servers = [{ url: API_BASE_URL, description: 'API Server' }];
  }

  if (spec.components?.schemas) {
    Object.keys(spec.components.schemas).forEach(schemaName => {
      if (!spec.components.schemas[schemaName].title) {
        spec.components.schemas[schemaName].title = schemaName;
      }
    });
  }

  Object.keys(spec.paths).forEach(pathKey => {
    Object.keys(spec.paths[pathKey]).forEach(method => {
      const operation = spec.paths[pathKey][method];
      if (!operation.operationId) {
        const pathParts = pathKey.split('/').filter(p => p && !p.startsWith('{'));
        const methodName = method.toLowerCase();
        operation.operationId = `${methodName}${pathParts.map(p =>
          p.charAt(0).toUpperCase() + p.slice(1)
        ).join('')}`;
      }
    });
  });

  return spec;
}

function generateTypeScriptClient(spec) {
  if (fs.existsSync(OUTPUT_DIR)) {
    fs.rmSync(OUTPUT_DIR, { recursive: true, force: true });
  }
  fs.mkdirSync(OUTPUT_DIR, { recursive: true });

  generateTypeScriptInterfaces(spec);
  generateApiClient(spec);
  generateClientHelpers(spec);
}

function generateTypeScriptInterfaces(spec) {
  const schemas = spec.components?.schemas || {};
  let interfaceContent = '';

  Object.entries(schemas).forEach(([schemaName, schema]) => {
    interfaceContent += generateInterface(schemaName, schema);
    interfaceContent += '\n\n';
  });

  fs.writeFileSync(path.join(OUTPUT_DIR, 'models.ts'), interfaceContent);
}

function generateInterface(name, schema) {
  const properties = schema.properties || {};
  const required = schema.required || [];

  let interfaceCode = `export interface ${name} {\n`;

  Object.entries(properties).forEach(([propName, propSchema]) => {
    const isRequired = required.includes(propName);
    const optional = isRequired ? '' : '?';
    const type = getTypeScriptType(propSchema);

    interfaceCode += `  ${propName}${optional}: ${type};\n`;
  });

  interfaceCode += '}';
  return interfaceCode;
}

function getTypeScriptType(schema) {
  if (schema.$ref) {
    return schema.$ref.split('/').pop();
  }

  if (schema.allOf) {
    const ref = schema.allOf.find(item => item.$ref);
    return ref ? ref.$ref.split('/').pop() : 'any';
  }

  switch (schema.type) {
    case 'string':
      if (schema.enum) {
        return schema.enum.map(val => `'${val}'`).join(' | ');
      }
      return 'string';
    case 'number':
    case 'integer':
      return 'number';
    case 'boolean':
      return 'boolean';
    case 'array':
      const itemType = getTypeScriptType(schema.items || { type: 'any' });
      return `${itemType}[]`;
    case 'object':
      return 'any';
    default:
      return 'any';
  }
}

function generateApiClient(spec) {
  let apiContent = `export interface ApiConfig {
  basePath?: string;
  apiKey?: string;
  headers?: Record<string, string>;
}

export class BaseCryptoIndicatorApiClient {
  private basePath: string;
  private headers: Record<string, string>;

  constructor(config: ApiConfig = {}) {
    this.basePath = config.basePath || '';
    this.headers = {
      'Content-Type': 'application/json',
      ...config.headers,
    };

    if (config.apiKey) {
      this.headers['Authorization'] = 'Bearer ' + config.apiKey;
    }
  }

  protected async request<T>(
    method: string,
    path: string,
    body?: any
  ): Promise<T> {
    const url = this.basePath + path;

    const options: RequestInit = {
      method,
      headers: this.headers,
    };

    if (body) {
      options.body = JSON.stringify(body);
    }

    const response = await fetch(url, options);

    if (!response.ok) {
      throw new Error('API Error: ' + response.status + ' ' + response.statusText);
    }

    return response.json();
  }
}
`;

  fs.writeFileSync(path.join(OUTPUT_DIR, 'api.ts'), apiContent);
}

function generateApiMethod(method, path, operation) {
  const operationId = operation.operationId || `${method}${path.replace(/[^a-zA-Z0-9]/g, '')}`;

  const successResponse = operation.responses?.['200'];
  const responseSchema = successResponse?.content?.['application/json']?.schema;
  let returnType = 'any';

  if (responseSchema) {
    if (responseSchema.type === 'array' && responseSchema.items?.$ref) {
      const itemType = responseSchema.items.$ref.split('/').pop();
      returnType = `${itemType}[]`;
    } else if (responseSchema.$ref) {
      returnType = responseSchema.$ref.split('/').pop();
    }
  }

  return `
  async ${operationId}(): Promise<${returnType}> {
    return this.request<${returnType}>('${method.toUpperCase()}', '${path}');
  }
`;
}

function generateClientHelpers(spec) {
  const paths = spec.paths || {};
  let apiMethods = '';

  Object.entries(paths).forEach(([pathKey, pathItem]) => {
    Object.entries(pathItem).forEach(([method, operation]) => {
      if (method === 'get' || method === 'post' || method === 'put' || method === 'delete') {
        apiMethods += generateApiMethod(method, pathKey, operation);
      }
    });
  });

  const clientWrapperContent = `import {ApiConfig, BaseCryptoIndicatorApiClient as BaseApiClient} from "./api";
import type { StructuredCryptoCurrencyStatisticsResultDto } from "./models";

export interface ApiClientConfig extends ApiConfig {
  basePath?: string;
  apiKey?: string;
  accessToken?: string;
}

export class CryptoIndicatorApiClient extends BaseApiClient {
  constructor(config: ApiClientConfig = {}) {
    super({
      basePath: config.basePath || '${CLIENT_BASE_URL}',
      headers: {
        ...(config.apiKey && { 'Authorization': 'Bearer ' + config.apiKey }),
        ...(config.accessToken && { 'Authorization': 'Bearer ' + config.accessToken }),
      }
    });
  }
${apiMethods}}

export * from './models';
export { BaseCryptoIndicatorApiClient } from './api';
export type { ApiConfig } from './api';
export const defaultApiClient = new CryptoIndicatorApiClient();
`;

  fs.writeFileSync(path.join(OUTPUT_DIR, 'client.ts'), clientWrapperContent);

  const indexContent = `export * from './client';
export * from './models';
export { BaseCryptoIndicatorApiClient } from './api';
export type { ApiConfig } from './api';
`;

  fs.writeFileSync(path.join(OUTPUT_DIR, 'index.ts'), indexContent);
}

async function main() {
  try {
    const spec = await loadOpenAPISpec();
    generateTypeScriptClient(spec);
  } catch (error) {
    console.error('Client generation failed:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { main, loadOpenAPISpec, generateTypeScriptClient };
