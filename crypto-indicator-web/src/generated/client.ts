import {ApiConfig, BaseCryptoIndicatorApiClient as BaseApiClient} from "./api";
import type { StructuredCryptoCurrencyStatisticsResultDto } from "./models";

export interface ApiClientConfig extends ApiConfig {
  basePath?: string;
  apiKey?: string;
  accessToken?: string;
}

export class CryptoIndicatorApiClient extends BaseApiClient {
  constructor(config: ApiClientConfig = {}) {
    super({
      basePath: config.basePath || '',
      headers: {
        ...(config.apiKey && { 'Authorization': 'Bearer ' + config.apiKey }),
        ...(config.accessToken && { 'Authorization': 'Bearer ' + config.accessToken }),
      }
    });
  }

  async CryptoStatisticsController_getCryptoStatistics(): Promise<StructuredCryptoCurrencyStatisticsResultDto[]> {
    return this.request<StructuredCryptoCurrencyStatisticsResultDto[]>('GET', '/api/v1/crypto/statistics');
  }
}

export * from './models';
export { BaseCryptoIndicatorApiClient } from './api';
export type { ApiConfig } from './api';
export const defaultApiClient = new CryptoIndicatorApiClient();
