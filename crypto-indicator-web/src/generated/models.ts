export interface LatestIndicatorDataDto {
  close: number;
  color: 'gold' | 'blue' | 'gray' | 'green' | 'red';
  high: number;
  hl2: number;
  low: number;
  marketCap: number;
  name: string;
  open: number;
  p1: boolean;
  p2: boolean;
  p3: boolean;
  smma15: number;
  smma19: number;
  smma25: number;
  smma29: number;
  timestamp: string;
  volume: number;
}

export interface StructuredCryptoCurrencyStatisticsResultDto {
  symbol: string;
  rank: number;
  latest_indicator_data_in_usd?: LatestIndicatorDataDto;
  latest_indicator_data_in_btc?: LatestIndicatorDataDto;
}

