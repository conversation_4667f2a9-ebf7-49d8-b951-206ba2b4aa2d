import { <PERSON>, Get, Logger } from '@nestjs/common';
import { ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { CryptoStatisticsService } from './crypto.statistics.service';
import { StructuredCryptoCurrencyStatisticsResultDto } from './dto/crypto-statistics.dto';
import { CryptoCurrency } from './api/financial.indicator.api';

@ApiTags('Crypto Statistics')
@Controller()
export class CryptoStatisticsController {
  private readonly logger = new Logger(CryptoStatisticsController.name);

  constructor(
    private readonly cryptoStatisticsService: CryptoStatisticsService,
  ) {}

  @Get('/api/v1/crypto/statistics')
  @ApiOperation({ summary: 'Get cryptocurrency statistics' })
  @ApiOkResponse({ type: [StructuredCryptoCurrencyStatisticsResultDto] })
  async getCryptoStatistics(): Promise<CryptoCurrency[]> {
    this.logger.log('Getting crypto statistics');
    return await this.cryptoStatisticsService.getStructuredStatistics();
  }
}
