import { ApiProperty } from '@nestjs/swagger';

export class LatestIndicatorDataDto {
  @ApiProperty() close: number;
  @ApiProperty({ enum: ['gold', 'blue', 'gray', 'green', 'red'] })
  color: string;
  @ApiProperty() high: number;
  @ApiProperty() hl2: number;
  @ApiProperty() low: number;
  @ApiProperty() marketCap: number;
  @ApiProperty() name: string;
  @ApiProperty() open: number;
  @ApiProperty() p1: boolean;
  @ApiProperty() p2: boolean;
  @ApiProperty() p3: boolean;
  @ApiProperty() smma15: number;
  @ApiProperty() smma19: number;
  @ApiProperty() smma25: number;
  @ApiProperty() smma29: number;
  @ApiProperty() timestamp: string;
  @ApiProperty() volume: number;
}

export class StructuredCryptoCurrencyStatisticsResultDto {
  @ApiProperty() symbol: string;
  @ApiProperty() conversionCurrency: number;
  @ApiProperty() indicatorValues: LatestIndicatorDataDto;
}
