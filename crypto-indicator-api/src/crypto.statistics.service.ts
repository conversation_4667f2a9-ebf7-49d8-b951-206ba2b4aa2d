import { Inject, Injectable } from '@nestjs/common';
import {
  CryptoCurrency,
  FinancialIndicatorApi,
} from './api/financial.indicator.api';

@Injectable()
export class CryptoStatisticsService {
  constructor(
    @Inject('financialIndicatorApi')
    private readonly financialIndicatorApi: FinancialIndicatorApi,
  ) {}

  async getStructuredStatistics(): Promise<CryptoCurrency[]> {
    return await this.financialIndicatorApi.getCryptoStatistics();
  }
}
